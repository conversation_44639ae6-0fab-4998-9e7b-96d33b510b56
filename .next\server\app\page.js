/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cdentalcare.id%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2RlbnRhbGNhcmUuaWQlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUQlM0ElNUNkZW50YWxjYXJlLmlkJTVDc3JjJTVDYXBwJTVDcHJvdmlkZXJzLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLz8xYWFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZGVudGFsY2FyZS5pZFxcXFxzcmNcXFxcYXBwXFxcXHByb3ZpZGVycy50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2RlbnRhbGNhcmUuaWQlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLz8yZWYwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcZGVudGFsY2FyZS5pZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cdentalcare.id%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout/Header */ \"(ssr)/./src/components/Layout/Header.tsx\");\n/* harmony import */ var _components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Dashboard/StatsCard */ \"(ssr)/./src/components/Dashboard/StatsCard.tsx\");\n/* harmony import */ var _components_Dashboard_TodayAppointments__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Dashboard/TodayAppointments */ \"(ssr)/./src/components/Dashboard/TodayAppointments.tsx\");\n/* harmony import */ var _hooks_useDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDashboard */ \"(ssr)/./src/hooks/useDashboard.ts\");\n/* harmony import */ var _hooks_usePatients__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePatients */ \"(ssr)/./src/hooks/usePatients.ts\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,ExclamationTriangleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,ExclamationTriangleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,ExclamationTriangleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,ExclamationTriangleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,ExclamationTriangleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClockIcon,CurrencyDollarIcon,ExclamationTriangleIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Dashboard() {\n    const { data: patients = [] } = (0,_hooks_usePatients__WEBPACK_IMPORTED_MODULE_5__.usePatients)();\n    const stats = (0,_hooks_useDashboard__WEBPACK_IMPORTED_MODULE_4__.useRealTimeDashboard)();\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"id-ID\", {\n            style: \"currency\",\n            currency: \"IDR\",\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                title: \"Dashboard\",\n                subtitle: \"Selamat datang di sistem manajemen klinik gigi\"\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                title: \"Appointment Hari Ini\",\n                                value: stats.todayAppointments,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"blue\",\n                                trend: {\n                                    value: 12,\n                                    isPositive: true\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                title: \"Pendapatan Hari Ini\",\n                                value: formatCurrency(stats.todayRevenue),\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"green\",\n                                trend: {\n                                    value: 8,\n                                    isPositive: true\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                title: \"Total Pasien\",\n                                value: patients.length,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"purple\",\n                                trend: {\n                                    value: 15,\n                                    isPositive: true\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                title: \"Pendapatan Bulan Ini\",\n                                value: formatCurrency(stats.monthlyRevenue),\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"blue\",\n                                trend: {\n                                    value: 5,\n                                    isPositive: true\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                title: \"Pembayaran Tertunda\",\n                                value: stats.pendingPayments,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"yellow\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_StatsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                title: \"Stok Menipis\",\n                                value: stats.lowStockItems,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClockIcon_CurrencyDollarIcon_ExclamationTriangleIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, void 0, void 0),\n                                color: \"red\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_TodayAppointments__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Aksi Cepat\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full btn-primary text-left\",\n                                                        children: \"+ Tambah Pasien Baru\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full btn-secondary text-left\",\n                                                        children: \"+ Buat Appointment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full btn-secondary text-left\",\n                                                        children: \"\\uD83D\\uDCCB Lihat Jadwal Hari Ini\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full btn-secondary text-left\",\n                                                        children: \"\\uD83D\\uDCB0 Buat Invoice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Aktivitas Terbaru\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Pembayaran dari Budi Santoso diterima\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Appointment baru dari Sari Dewi\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Stok Anestesi Lidocaine menipis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Treatment selesai untuk Andi Wijaya\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnRDtBQUNTO0FBQ2dCO0FBQ2I7QUFDVjtBQVFiO0FBRXRCLFNBQVNXO0lBQ3RCLE1BQU0sRUFBRUMsTUFBTUMsV0FBVyxFQUFFLEVBQUUsR0FBR1QsK0RBQVdBO0lBQzNDLE1BQU1VLFFBQVFYLHlFQUFvQkE7SUFFbEMsTUFBTVksaUJBQWlCLENBQUNDO1FBQ3RCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyx1QkFBdUI7UUFDekIsR0FBR0MsTUFBTSxDQUFDTjtJQUNaO0lBRUEscUJBQ0UsOERBQUNPO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDeEIsaUVBQU1BO2dCQUNMeUIsT0FBTTtnQkFDTkMsVUFBUzs7Ozs7OzBCQUdYLDhEQUFDQztnQkFBS0gsV0FBVTs7a0NBRWQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3ZCLHVFQUFTQTtnQ0FDUndCLE9BQU07Z0NBQ05HLE9BQU9kLE1BQU1lLGlCQUFpQjtnQ0FDOUJDLG9CQUFNLDhEQUFDekIsMExBQWdCQTtvQ0FBQ21CLFdBQVU7O2dDQUNsQ08sT0FBTTtnQ0FDTkMsT0FBTztvQ0FBRUosT0FBTztvQ0FBSUssWUFBWTtnQ0FBSzs7Ozs7OzBDQUd2Qyw4REFBQ2hDLHVFQUFTQTtnQ0FDUndCLE9BQU07Z0NBQ05HLE9BQU9iLGVBQWVELE1BQU1vQixZQUFZO2dDQUN4Q0osb0JBQU0sOERBQUN4QiwwTEFBa0JBO29DQUFDa0IsV0FBVTs7Z0NBQ3BDTyxPQUFNO2dDQUNOQyxPQUFPO29DQUFFSixPQUFPO29DQUFHSyxZQUFZO2dDQUFLOzs7Ozs7MENBR3RDLDhEQUFDaEMsdUVBQVNBO2dDQUNSd0IsT0FBTTtnQ0FDTkcsT0FBT2YsU0FBU3NCLE1BQU07Z0NBQ3RCTCxvQkFBTSw4REFBQ3ZCLDBMQUFhQTtvQ0FBQ2lCLFdBQVU7O2dDQUMvQk8sT0FBTTtnQ0FDTkMsT0FBTztvQ0FBRUosT0FBTztvQ0FBSUssWUFBWTtnQ0FBSzs7Ozs7OzBDQUd2Qyw4REFBQ2hDLHVFQUFTQTtnQ0FDUndCLE9BQU07Z0NBQ05HLE9BQU9iLGVBQWVELE1BQU1zQixjQUFjO2dDQUMxQ04sb0JBQU0sOERBQUN0QiwwTEFBWUE7b0NBQUNnQixXQUFVOztnQ0FDOUJPLE9BQU07Z0NBQ05DLE9BQU87b0NBQUVKLE9BQU87b0NBQUdLLFlBQVk7Z0NBQUs7Ozs7OzswQ0FHdEMsOERBQUNoQyx1RUFBU0E7Z0NBQ1J3QixPQUFNO2dDQUNORyxPQUFPZCxNQUFNdUIsZUFBZTtnQ0FDNUJQLG9CQUFNLDhEQUFDcEIsMkxBQVNBO29DQUFDYyxXQUFVOztnQ0FDM0JPLE9BQU07Ozs7OzswQ0FHUiw4REFBQzlCLHVFQUFTQTtnQ0FDUndCLE9BQU07Z0NBQ05HLE9BQU9kLE1BQU13QixhQUFhO2dDQUMxQlIsb0JBQU0sOERBQUNyQiwyTEFBdUJBO29DQUFDZSxXQUFVOztnQ0FDekNPLE9BQU07Ozs7Ozs7Ozs7OztrQ0FLViw4REFBQ1I7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3RCLCtFQUFpQkE7Ozs7Ozs7Ozs7MENBSXBCLDhEQUFDcUI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNlO2dEQUFHZixXQUFVOzBEQUEyQzs7Ozs7OzBEQUN6RCw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDZ0I7d0RBQU9oQixXQUFVO2tFQUErQjs7Ozs7O2tFQUdqRCw4REFBQ2dCO3dEQUFPaEIsV0FBVTtrRUFBaUM7Ozs7OztrRUFHbkQsOERBQUNnQjt3REFBT2hCLFdBQVU7a0VBQWlDOzs7Ozs7a0VBR25ELDhEQUFDZ0I7d0RBQU9oQixXQUFVO2tFQUFpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU92RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZTtnREFBR2YsV0FBVTswREFBMkM7Ozs7OzswREFDekQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDaUI7Z0VBQUtqQixXQUFVOzBFQUFnQjs7Ozs7Ozs7Ozs7O2tFQUVsQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDaUI7Z0VBQUtqQixXQUFVOzBFQUFnQjs7Ozs7Ozs7Ozs7O2tFQUVsQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDaUI7Z0VBQUtqQixXQUFVOzBFQUFnQjs7Ozs7Ozs7Ozs7O2tFQUVsQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDaUI7Z0VBQUtqQixXQUFVOzBFQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2xEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL0xheW91dC9IZWFkZXInO1xuaW1wb3J0IFN0YXRzQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvRGFzaGJvYXJkL1N0YXRzQ2FyZCc7XG5pbXBvcnQgVG9kYXlBcHBvaW50bWVudHMgZnJvbSAnQC9jb21wb25lbnRzL0Rhc2hib2FyZC9Ub2RheUFwcG9pbnRtZW50cyc7XG5pbXBvcnQgeyB1c2VSZWFsVGltZURhc2hib2FyZCB9IGZyb20gJ0AvaG9va3MvdXNlRGFzaGJvYXJkJztcbmltcG9ydCB7IHVzZVBhdGllbnRzIH0gZnJvbSAnQC9ob29rcy91c2VQYXRpZW50cyc7XG5pbXBvcnQge1xuICBDYWxlbmRhckRheXNJY29uLFxuICBDdXJyZW5jeURvbGxhckljb24sXG4gIFVzZXJHcm91cEljb24sXG4gIENoYXJ0QmFySWNvbixcbiAgRXhjbGFtYXRpb25UcmlhbmdsZUljb24sXG4gIENsb2NrSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmQoKSB7XG4gIGNvbnN0IHsgZGF0YTogcGF0aWVudHMgPSBbXSB9ID0gdXNlUGF0aWVudHMoKTtcbiAgY29uc3Qgc3RhdHMgPSB1c2VSZWFsVGltZURhc2hib2FyZCgpO1xuXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnaWQtSUQnLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5OiAnSURSJyxcbiAgICAgIG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMCxcbiAgICB9KS5mb3JtYXQoYW1vdW50KTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWF1dG9cIj5cbiAgICAgIDxIZWFkZXJcbiAgICAgICAgdGl0bGU9XCJEYXNoYm9hcmRcIlxuICAgICAgICBzdWJ0aXRsZT1cIlNlbGFtYXQgZGF0YW5nIGRpIHNpc3RlbSBtYW5hamVtZW4ga2xpbmlrIGdpZ2lcIlxuICAgICAgLz5cblxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgIHsvKiBTdGF0cyBDYXJkcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIHhsOmdyaWQtY29scy02IGdhcC02IG1iLThcIj5cbiAgICAgICAgICA8U3RhdHNDYXJkXG4gICAgICAgICAgICB0aXRsZT1cIkFwcG9pbnRtZW50IEhhcmkgSW5pXCJcbiAgICAgICAgICAgIHZhbHVlPXtzdGF0cy50b2RheUFwcG9pbnRtZW50c31cbiAgICAgICAgICAgIGljb249ezxDYWxlbmRhckRheXNJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPn1cbiAgICAgICAgICAgIGNvbG9yPVwiYmx1ZVwiXG4gICAgICAgICAgICB0cmVuZD17eyB2YWx1ZTogMTIsIGlzUG9zaXRpdmU6IHRydWUgfX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPFN0YXRzQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJQZW5kYXBhdGFuIEhhcmkgSW5pXCJcbiAgICAgICAgICAgIHZhbHVlPXtmb3JtYXRDdXJyZW5jeShzdGF0cy50b2RheVJldmVudWUpfVxuICAgICAgICAgICAgaWNvbj17PEN1cnJlbmN5RG9sbGFySWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz59XG4gICAgICAgICAgICBjb2xvcj1cImdyZWVuXCJcbiAgICAgICAgICAgIHRyZW5kPXt7IHZhbHVlOiA4LCBpc1Bvc2l0aXZlOiB0cnVlIH19XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIDxTdGF0c0NhcmRcbiAgICAgICAgICAgIHRpdGxlPVwiVG90YWwgUGFzaWVuXCJcbiAgICAgICAgICAgIHZhbHVlPXtwYXRpZW50cy5sZW5ndGh9XG4gICAgICAgICAgICBpY29uPXs8VXNlckdyb3VwSWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz59XG4gICAgICAgICAgICBjb2xvcj1cInB1cnBsZVwiXG4gICAgICAgICAgICB0cmVuZD17eyB2YWx1ZTogMTUsIGlzUG9zaXRpdmU6IHRydWUgfX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPFN0YXRzQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJQZW5kYXBhdGFuIEJ1bGFuIEluaVwiXG4gICAgICAgICAgICB2YWx1ZT17Zm9ybWF0Q3VycmVuY3koc3RhdHMubW9udGhseVJldmVudWUpfVxuICAgICAgICAgICAgaWNvbj17PENoYXJ0QmFySWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz59XG4gICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxuICAgICAgICAgICAgdHJlbmQ9e3sgdmFsdWU6IDUsIGlzUG9zaXRpdmU6IHRydWUgfX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPFN0YXRzQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJQZW1iYXlhcmFuIFRlcnR1bmRhXCJcbiAgICAgICAgICAgIHZhbHVlPXtzdGF0cy5wZW5kaW5nUGF5bWVudHN9XG4gICAgICAgICAgICBpY29uPXs8Q2xvY2tJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPn1cbiAgICAgICAgICAgIGNvbG9yPVwieWVsbG93XCJcbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPFN0YXRzQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJTdG9rIE1lbmlwaXNcIlxuICAgICAgICAgICAgdmFsdWU9e3N0YXRzLmxvd1N0b2NrSXRlbXN9XG4gICAgICAgICAgICBpY29uPXs8RXhjbGFtYXRpb25UcmlhbmdsZUljb24gY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+fVxuICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgR3JpZCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgey8qIFRvZGF5J3MgQXBwb2ludG1lbnRzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgPFRvZGF5QXBwb2ludG1lbnRzIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIFF1aWNrIEFjdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPkFrc2kgQ2VwYXQ8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIGJ0bi1wcmltYXJ5IHRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgKyBUYW1iYWggUGFzaWVuIEJhcnVcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInctZnVsbCBidG4tc2Vjb25kYXJ5IHRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgKyBCdWF0IEFwcG9pbnRtZW50XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLXNlY29uZGFyeSB0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgIPCfk4sgTGloYXQgSmFkd2FsIEhhcmkgSW5pXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLXNlY29uZGFyeSB0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgIPCfkrAgQnVhdCBJbnZvaWNlXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBSZWNlbnQgQWN0aXZpdHkgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5Ba3Rpdml0YXMgVGVyYmFydTwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTUwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5QZW1iYXlhcmFuIGRhcmkgQnVkaSBTYW50b3NvIGRpdGVyaW1hPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5BcHBvaW50bWVudCBiYXJ1IGRhcmkgU2FyaSBEZXdpPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy15ZWxsb3ctNTAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlN0b2sgQW5lc3Rlc2kgTGlkb2NhaW5lIG1lbmlwaXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXB1cnBsZS01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+VHJlYXRtZW50IHNlbGVzYWkgdW50dWsgQW5kaSBXaWpheWE8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIlN0YXRzQ2FyZCIsIlRvZGF5QXBwb2ludG1lbnRzIiwidXNlUmVhbFRpbWVEYXNoYm9hcmQiLCJ1c2VQYXRpZW50cyIsIkNhbGVuZGFyRGF5c0ljb24iLCJDdXJyZW5jeURvbGxhckljb24iLCJVc2VyR3JvdXBJY29uIiwiQ2hhcnRCYXJJY29uIiwiRXhjbGFtYXRpb25UcmlhbmdsZUljb24iLCJDbG9ja0ljb24iLCJEYXNoYm9hcmQiLCJkYXRhIiwicGF0aWVudHMiLCJzdGF0cyIsImZvcm1hdEN1cnJlbmN5IiwiYW1vdW50IiwiSW50bCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwiY3VycmVuY3kiLCJtaW5pbXVtRnJhY3Rpb25EaWdpdHMiLCJmb3JtYXQiLCJkaXYiLCJjbGFzc05hbWUiLCJ0aXRsZSIsInN1YnRpdGxlIiwibWFpbiIsInZhbHVlIiwidG9kYXlBcHBvaW50bWVudHMiLCJpY29uIiwiY29sb3IiLCJ0cmVuZCIsImlzUG9zaXRpdmUiLCJ0b2RheVJldmVudWUiLCJsZW5ndGgiLCJtb250aGx5UmV2ZW51ZSIsInBlbmRpbmdQYXltZW50cyIsImxvd1N0b2NrSXRlbXMiLCJoMyIsImJ1dHRvbiIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _lib_queryClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/queryClient */ \"(ssr)/./src/lib/queryClient.ts\");\n/* harmony import */ var _components_Layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/AppLayout */ \"(ssr)/./src/components/Layout/AppLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: _lib_queryClient__WEBPACK_IMPORTED_MODULE_3__.queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.TenantProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__.AppLayout, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTREO0FBQzVELHVFQUF1RTtBQUNqQjtBQUNJO0FBQ1Y7QUFDVTtBQUVuRCxTQUFTSyxVQUFVLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkUscUJBQ0UsOERBQUNOLHNFQUFtQkE7UUFBQ08sUUFBUUoseURBQVdBO2tCQUN0Qyw0RUFBQ0YsK0RBQVlBO3NCQUNYLDRFQUFDQyxtRUFBY0E7MEJBQ2IsNEVBQUNFLG1FQUFTQTs4QkFDUEU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeD85MzI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG4vLyBpbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBUZW5hbnRQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgeyBxdWVyeUNsaWVudCB9IGZyb20gJ0AvbGliL3F1ZXJ5Q2xpZW50JztcbmltcG9ydCB7IEFwcExheW91dCB9IGZyb20gJ0AvY29tcG9uZW50cy9MYXlvdXQvQXBwTGF5b3V0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICA8VGVuYW50UHJvdmlkZXI+XG4gICAgICAgICAgPEFwcExheW91dD5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L0FwcExheW91dD5cbiAgICAgICAgPC9UZW5hbnRQcm92aWRlcj5cbiAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgey8qIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAoXG4gICAgICAgIDxSZWFjdFF1ZXJ5RGV2dG9vbHMgaW5pdGlhbElzT3Blbj17ZmFsc2V9IC8+XG4gICAgICApfSAqL31cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUXVlcnlDbGllbnRQcm92aWRlciIsIkF1dGhQcm92aWRlciIsIlRlbmFudFByb3ZpZGVyIiwicXVlcnlDbGllbnQiLCJBcHBMYXlvdXQiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/Auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \n\n\n\n\n// Demo data setup removed\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signIn } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            await signIn(email, password);\n            router.push(\"/\");\n        } catch (error) {\n            setError(getErrorMessage(error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getErrorMessage = (errorCode)=>{\n        switch(errorCode){\n            case \"auth/user-not-found\":\n                return \"Email tidak terdaftar\";\n            case \"auth/wrong-password\":\n                return \"Password salah\";\n            case \"auth/invalid-email\":\n                return \"Format email tidak valid\";\n            case \"auth/user-disabled\":\n                return \"Akun telah dinonaktifkan\";\n            case \"auth/too-many-requests\":\n                return \"Terlalu banyak percobaan login. Coba lagi nanti\";\n            default:\n                return \"Terjadi kesalahan saat login\";\n        }\n    };\n    // Demo accounts for testing\n    const demoAccounts = [\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Dokter\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Resepsionis\"\n        },\n        {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            role: \"Admin\"\n        }\n    ];\n    const handleDemoLogin = (demoEmail, demoPassword)=>{\n        setEmail(demoEmail);\n        setPassword(demoPassword);\n    };\n    const handleSetupDemo = async ()=>{\n        setSetupLoading(true);\n        setError(\"\");\n        try {\n            // Demo data setup functionality removed\n            // Real data is now managed through Firebase services\n            alert(\"Demo data setup tidak tersedia. Gunakan akun demo yang sudah ada atau buat data melalui aplikasi.\");\n        } catch (error) {\n            setError(\"Error: \" + error.message);\n        } finally{\n            setSetupLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-16 w-16 bg-primary-600 rounded-xl flex items-center justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-2xl\",\n                                children: \"DC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Login ke DentalCare\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Sistem Manajemen Klinik Gigi\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-green-900 mb-2\",\n                            children: \"Demo Mode:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSetupDemo,\n                            disabled: setupLoading,\n                            className: \"w-full bg-green-600 hover:bg-green-700 text-white text-sm py-2 px-4 rounded disabled:opacity-50\",\n                            children: setupLoading ? \"Checking...\" : \"Info Demo Data\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-700 mt-1\",\n                            children: \"Aplikasi menggunakan data real dari Firebase. Gunakan akun demo di bawah untuk testing.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                            children: \"Demo Accounts:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleDemoLogin(account.email, account.password),\n                                    className: \"block w-full text-left text-xs text-blue-700 hover:text-blue-900 hover:bg-blue-100 px-2 py-1 rounded\",\n                                    children: [\n                                        account.role,\n                                        \": \",\n                                        account.email\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"input-field\",\n                                            placeholder: \"Masukkan email Anda\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    className: \"input-field pr-10\",\n                                                    placeholder: \"Masukkan password Anda\",\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"remember-me\",\n                                            name: \"remember-me\",\n                                            type: \"checkbox\",\n                                            className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"remember-me\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: \"Ingat saya\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                        children: \"Lupa password?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full btn-primary flex justify-center items-center\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Logging in...\"\n                                ]\n                            }, void 0, true) : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"\\xa9 2024 DentalCare. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Auth\\\\LoginForm.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/StatsCard.tsx":
/*!************************************************!*\
  !*** ./src/components/Dashboard/StatsCard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst colorClasses = {\n    blue: \"bg-blue-500\",\n    green: \"bg-green-500\",\n    yellow: \"bg-yellow-500\",\n    red: \"bg-red-500\",\n    purple: \"bg-purple-500\"\n};\nfunction StatsCard({ title, value, icon, trend, color }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-12 h-12 ${colorClasses[color]} rounded-lg flex items-center justify-center text-white`,\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-600\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: `text-sm ${trend.isPositive ? \"text-green-600\" : \"text-red-600\"}`,\n                            children: [\n                                trend.isPositive ? \"+\" : \"-\",\n                                Math.abs(trend.value),\n                                \"%\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 ml-1\",\n                                    children: \"dari bulan lalu\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\StatsCard.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/StatsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard/TodayAppointments.tsx":
/*!********************************************************!*\
  !*** ./src/components/Dashboard/TodayAppointments.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TodayAppointments)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAppointments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAppointments */ \"(ssr)/./src/hooks/useAppointments.ts\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TodayAppointments() {\n    const { data: todayAppointments = [], isLoading, error } = (0,_hooks_useAppointments__WEBPACK_IMPORTED_MODULE_1__.useTodayAppointments)();\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"scheduled\":\n                return \"status-scheduled\";\n            case \"confirmed\":\n                return \"status-confirmed\";\n            case \"in-progress\":\n                return \"status-in-progress\";\n            case \"completed\":\n                return \"status-completed\";\n            case \"cancelled\":\n                return \"status-cancelled\";\n            default:\n                return \"status-scheduled\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"scheduled\":\n                return \"Terjadwal\";\n            case \"confirmed\":\n                return \"Dikonfirmasi\";\n            case \"in-progress\":\n                return \"Berlangsung\";\n            case \"completed\":\n                return \"Selesai\";\n            case \"cancelled\":\n                return \"Dibatalkan\";\n            default:\n                return status;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Jadwal Hari Ini\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            todayAppointments.length,\n                            \" appointment\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Memuat jadwal...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 text-red-600\",\n                children: [\n                    \"Error memuat jadwal: \",\n                    error.message\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this),\n            !isLoading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    todayAppointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: appointment.patientName\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: appointment.type\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"Dr. \",\n                                                        appointment.doctorName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-gray-600 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this),\n                                                appointment.time,\n                                                \" (\",\n                                                appointment.duration,\n                                                \" menit)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `status-badge ${getStatusColor(appointment.status)}`,\n                                            children: getStatusText(appointment.status)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, appointment.id, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)),\n                    todayAppointments.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: \"Tidak ada appointment hari ini\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Dashboard\\\\TodayAppointments.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard/TodayAppointments.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/Layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Auth/LoginForm */ \"(ssr)/./src/components/Auth/LoginForm.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Layout/Sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\nfunction AppLayout({ children }) {\n    const { user, profile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Show loading spinner while checking auth\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this);\n    }\n    // Show login form if not authenticated\n    if (!user || !profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Auth_LoginForm__WEBPACK_IMPORTED_MODULE_2__.LoginForm, {}, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n            lineNumber: 26,\n            columnNumber: 12\n        }, this);\n    }\n    // Show main app layout for authenticated users\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\AppLayout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _components_TenantManagement_TenantSwitcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TenantManagement/TenantSwitcher */ \"(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header({ title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TenantManagement_TenantSwitcher__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-64\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Cari pasien, appointment...\",\n                                    className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-80\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"relative p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: new Date().toLocaleDateString(\"id-ID\", {\n                                weekday: \"long\",\n                                year: \"numeric\",\n                                month: \"long\",\n                                day: \"numeric\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRTRFO0FBQ0Y7QUFPM0QsU0FBU0csT0FBTyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBZTtJQUM3RCxxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQzs7c0NBQ0MsOERBQUNDOzRCQUFHRixXQUFVO3NDQUFvQ0g7Ozs7Ozt3QkFDakRDLDBCQUNDLDhEQUFDSzs0QkFBRUgsV0FBVTtzQ0FBOEJGOzs7Ozs7Ozs7Ozs7OEJBSS9DLDhEQUFDRztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNMLG1GQUFjQTs0QkFBQ0ssV0FBVTs7Ozs7O3NDQUcxQiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDTixzSEFBbUJBO29DQUFDTSxXQUFVOzs7Ozs7OENBQy9CLDhEQUFDSTtvQ0FDQ0MsTUFBSztvQ0FDTEMsYUFBWTtvQ0FDWk4sV0FBVTs7Ozs7Ozs7Ozs7O3NDQUtkLDhEQUFDTzs0QkFBT1AsV0FBVTs7OENBQ2hCLDhEQUFDUCxzSEFBUUE7b0NBQUNPLFdBQVU7Ozs7Ozs4Q0FDcEIsOERBQUNRO29DQUFLUixXQUFVOzs7Ozs7Ozs7Ozs7c0NBSWxCLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDWixJQUFJUyxPQUFPQyxrQkFBa0IsQ0FBQyxTQUFTO2dDQUN0Q0MsU0FBUztnQ0FDVEMsTUFBTTtnQ0FDTkMsT0FBTztnQ0FDUEMsS0FBSzs0QkFDUDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL2RlbnRhbC1jbGluaWMtdWkvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvSGVhZGVyLnRzeD8zOWE4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgQmVsbEljb24sIE1hZ25pZnlpbmdHbGFzc0ljb24gfSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IFRlbmFudFN3aXRjaGVyIGZyb20gJ0AvY29tcG9uZW50cy9UZW5hbnRNYW5hZ2VtZW50L1RlbmFudFN3aXRjaGVyJztcblxuaW50ZXJmYWNlIEhlYWRlclByb3BzIHtcbiAgdGl0bGU6IHN0cmluZztcbiAgc3VidGl0bGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhlYWRlcih7IHRpdGxlLCBzdWJ0aXRsZSB9OiBIZWFkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktNFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj57dGl0bGV9PC9oMT5cbiAgICAgICAgICB7c3VidGl0bGUgJiYgKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTFcIj57c3VidGl0bGV9PC9wPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICB7LyogVGVuYW50IFN3aXRjaGVyICovfVxuICAgICAgICAgIDxUZW5hbnRTd2l0Y2hlciBjbGFzc05hbWU9XCJ3LTY0XCIgLz5cblxuICAgICAgICAgIHsvKiBTZWFyY2ggKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgPE1hZ25pZnlpbmdHbGFzc0ljb24gY2xhc3NOYW1lPVwidy01IGgtNSBhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ2FyaSBwYXNpZW4sIGFwcG9pbnRtZW50Li4uXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcHItNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHctODBcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBOb3RpZmljYXRpb25zICovfVxuICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicmVsYXRpdmUgcC0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgPEJlbGxJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgcmlnaHQtMCB3LTIgaC0yIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgey8qIEN1cnJlbnQgRGF0ZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAge25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdpZC1JRCcsIHtcbiAgICAgICAgICAgICAgd2Vla2RheTogJ2xvbmcnLFxuICAgICAgICAgICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgICAgIGRheTogJ251bWVyaWMnXG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJCZWxsSWNvbiIsIk1hZ25pZnlpbmdHbGFzc0ljb24iLCJUZW5hbnRTd2l0Y2hlciIsIkhlYWRlciIsInRpdGxlIiwic3VidGl0bGUiLCJoZWFkZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJoMSIsInAiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImJ1dHRvbiIsInNwYW4iLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwid2Vla2RheSIsInllYXIiLCJtb250aCIsImRheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarDaysIcon,ChartBarIcon,ClipboardDocumentListIcon,CogIcon,CubeIcon,HomeIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        name: \"Pasien\",\n        href: \"/patients\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Jadwal\",\n        href: \"/appointments\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Treatment\",\n        href: \"/treatments\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Inventory\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Laporan\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Pengaturan\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col w-64 bg-white border-r border-gray-200 h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-sm\",\n                                children: \"DC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"DentalCare\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-6 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: item.href,\n                        className: `sidebar-link ${isActive ? \"active\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"w-5 h-5 mr-3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarDaysIcon_ChartBarIcon_ClipboardDocumentListIcon_CogIcon_CubeIcon_HomeIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-6 h-6 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                    children: \"Dr. Sarah Putri\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 truncate\",\n                                    children: \"Dokter Gigi\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx":
/*!************************************************************!*\
  !*** ./src/components/TenantManagement/TenantSwitcher.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TenantSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,CheckIcon,ChevronDownIcon,PlusIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TenantSwitcher({ className = \"\" }) {\n    const { tenant, tenantId, loading, switchTenant, createTenant } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_2__.useTenant)();\n    const { profile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTenantName, setNewTenantName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // For demo purposes, we'll show available tenants\n    // In a real app, you might fetch this from a user's accessible tenants\n    const availableTenants = tenantId ? [\n        {\n            id: tenantId,\n            name: tenant?.name || \"Current Clinic\"\n        }\n    ] : [];\n    const handleSwitchTenant = async (newTenantId)=>{\n        if (newTenantId === tenantId) {\n            setIsOpen(false);\n            return;\n        }\n        try {\n            await switchTenant(newTenantId);\n            setIsOpen(false);\n        } catch (error) {\n            console.error(\"Failed to switch tenant:\", error);\n        // You might want to show a toast notification here\n        }\n    };\n    const handleCreateTenant = async ()=>{\n        if (!newTenantName.trim()) return;\n        try {\n            setIsCreating(true);\n            await createTenant({\n                name: newTenantName.trim(),\n                address: \"\",\n                phone: \"\",\n                email: profile?.email || \"\"\n            });\n            setNewTenantName(\"\");\n            setIsOpen(false);\n        } catch (error) {\n            console.error(\"Failed to create tenant:\", error);\n        // You might want to show a toast notification here\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center space-x-2 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-gray-200 rounded-lg animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-4 bg-gray-200 rounded animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-3 w-full px-3 py-2 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900 truncate\",\n                                children: tenant?.name || \"Select Clinic\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 truncate\",\n                                children: tenant?.address || \"No address set\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: `w-4 h-4 text-gray-400 transition-transform ${isOpen ? \"transform rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: [\n                        availableTenants.map((availableTenant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSwitchTenant(availableTenant.id),\n                                className: \"flex items-center w-full px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: availableTenant.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableTenant.id === tenantId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, availableTenant.id, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-100 mt-1 pt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"New clinic name\",\n                                                value: newTenantName,\n                                                onChange: (e)=>setNewTenantName(e.target.value),\n                                                className: \"flex-1 px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                                                onKeyPress: (e)=>{\n                                                    if (e.key === \"Enter\") {\n                                                        handleCreateTenant();\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCreateTenant,\n                                                disabled: !newTenantName.trim() || isCreating,\n                                                className: \"flex items-center justify-center w-8 h-8 text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_CheckIcon_ChevronDownIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Create a new clinic\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\components\\\\TenantManagement\\\\TenantSwitcher.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UZW5hbnRNYW5hZ2VtZW50L1RlbmFudFN3aXRjaGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDb0I7QUFDSjtBQU1aO0FBTXRCLFNBQVNPLGVBQWUsRUFBRUMsWUFBWSxFQUFFLEVBQXVCO0lBQzVFLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLE9BQU8sRUFBRUMsWUFBWSxFQUFFQyxZQUFZLEVBQUUsR0FBR1osa0VBQVNBO0lBQzNFLE1BQU0sRUFBRWEsT0FBTyxFQUFFLEdBQUdaLDhEQUFPQTtJQUMzQixNQUFNLENBQUNhLFFBQVFDLFVBQVUsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ2lCLFlBQVlDLGNBQWMsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ21CLGVBQWVDLGlCQUFpQixHQUFHcEIsK0NBQVFBLENBQUM7SUFFbkQsa0RBQWtEO0lBQ2xELHVFQUF1RTtJQUN2RSxNQUFNcUIsbUJBQW1CWCxXQUFXO1FBQ2xDO1lBQUVZLElBQUlaO1lBQVVhLE1BQU1kLFFBQVFjLFFBQVE7UUFBaUI7S0FFeEQsR0FBRyxFQUFFO0lBRU4sTUFBTUMscUJBQXFCLE9BQU9DO1FBQ2hDLElBQUlBLGdCQUFnQmYsVUFBVTtZQUM1Qk0sVUFBVTtZQUNWO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTUosYUFBYWE7WUFDbkJULFVBQVU7UUFDWixFQUFFLE9BQU9VLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsbURBQW1EO1FBQ3JEO0lBQ0Y7SUFFQSxNQUFNRSxxQkFBcUI7UUFDekIsSUFBSSxDQUFDVCxjQUFjVSxJQUFJLElBQUk7UUFFM0IsSUFBSTtZQUNGWCxjQUFjO1lBQ2QsTUFBTUwsYUFBYTtnQkFDakJVLE1BQU1KLGNBQWNVLElBQUk7Z0JBQ3hCQyxTQUFTO2dCQUNUQyxPQUFPO2dCQUNQQyxPQUFPbEIsU0FBU2tCLFNBQVM7WUFDM0I7WUFDQVosaUJBQWlCO1lBQ2pCSixVQUFVO1FBQ1osRUFBRSxPQUFPVSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLG1EQUFtRDtRQUNyRCxTQUFVO1lBQ1JSLGNBQWM7UUFDaEI7SUFDRjtJQUVBLElBQUlQLFNBQVM7UUFDWCxxQkFDRSw4REFBQ3NCO1lBQUl6QixXQUFXLENBQUMsNEJBQTRCLEVBQUVBLFVBQVUsQ0FBQzs7OEJBQ3hELDhEQUFDeUI7b0JBQUl6QixXQUFVOzs7Ozs7OEJBQ2YsOERBQUN5QjtvQkFBSXpCLFdBQVU7Ozs7Ozs7Ozs7OztJQUdyQjtJQUVBLHFCQUNFLDhEQUFDeUI7UUFBSXpCLFdBQVcsQ0FBQyxTQUFTLEVBQUVBLFVBQVUsQ0FBQzs7MEJBQ3JDLDhEQUFDMEI7Z0JBQ0NDLFNBQVMsSUFBTW5CLFVBQVUsQ0FBQ0Q7Z0JBQzFCUCxXQUFVOztrQ0FFViw4REFBQ3lCO3dCQUFJekIsV0FBVTtrQ0FDYiw0RUFBQ0osK0lBQWtCQTs0QkFBQ0ksV0FBVTs7Ozs7Ozs7Ozs7a0NBRWhDLDhEQUFDeUI7d0JBQUl6QixXQUFVOzswQ0FDYiw4REFBQzRCO2dDQUFFNUIsV0FBVTswQ0FDVkMsUUFBUWMsUUFBUTs7Ozs7OzBDQUVuQiw4REFBQ2E7Z0NBQUU1QixXQUFVOzBDQUNWQyxRQUFRcUIsV0FBVzs7Ozs7Ozs7Ozs7O2tDQUd4Qiw4REFBQzNCLCtJQUFlQTt3QkFDZEssV0FBVyxDQUFDLDJDQUEyQyxFQUNyRE8sU0FBUyx5QkFBeUIsR0FDbkMsQ0FBQzs7Ozs7Ozs7Ozs7O1lBSUxBLHdCQUNDLDhEQUFDa0I7Z0JBQUl6QixXQUFVOzBCQUNiLDRFQUFDeUI7b0JBQUl6QixXQUFVOzt3QkFDWmEsaUJBQWlCZ0IsR0FBRyxDQUFDLENBQUNDLGdDQUNyQiw4REFBQ0o7Z0NBRUNDLFNBQVMsSUFBTVgsbUJBQW1CYyxnQkFBZ0JoQixFQUFFO2dDQUNwRGQsV0FBVTs7a0RBRVYsOERBQUN5Qjt3Q0FBSXpCLFdBQVU7a0RBQ2IsNEVBQUM0Qjs0Q0FBRTVCLFdBQVU7c0RBQ1Y4QixnQkFBZ0JmLElBQUk7Ozs7Ozs7Ozs7O29DQUd4QmUsZ0JBQWdCaEIsRUFBRSxLQUFLWiwwQkFDdEIsOERBQUNKLCtJQUFTQTt3Q0FBQ0UsV0FBVTs7Ozs7OzsrQkFWbEI4QixnQkFBZ0JoQixFQUFFOzs7OztzQ0FlM0IsOERBQUNXOzRCQUFJekIsV0FBVTtzQ0FDYiw0RUFBQ3lCO2dDQUFJekIsV0FBVTs7a0RBQ2IsOERBQUN5Qjt3Q0FBSXpCLFdBQVU7OzBEQUNiLDhEQUFDK0I7Z0RBQ0NDLE1BQUs7Z0RBQ0xDLGFBQVk7Z0RBQ1pDLE9BQU92QjtnREFDUHdCLFVBQVUsQ0FBQ0MsSUFBTXhCLGlCQUFpQndCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnREFDaERsQyxXQUFVO2dEQUNWc0MsWUFBWSxDQUFDRjtvREFDWCxJQUFJQSxFQUFFRyxHQUFHLEtBQUssU0FBUzt3REFDckJuQjtvREFDRjtnREFDRjs7Ozs7OzBEQUVGLDhEQUFDTTtnREFDQ0MsU0FBU1A7Z0RBQ1RvQixVQUFVLENBQUM3QixjQUFjVSxJQUFJLE1BQU1aO2dEQUNuQ1QsV0FBVTswREFFVFMsMkJBQ0MsOERBQUNnQjtvREFBSXpCLFdBQVU7Ozs7O3lFQUVmLDhEQUFDSCwrSUFBUUE7b0RBQUNHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUkxQiw4REFBQzRCO3dDQUFFNUIsV0FBVTtrREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVbkRPLHdCQUNDLDhEQUFDa0I7Z0JBQ0N6QixXQUFVO2dCQUNWMkIsU0FBUyxJQUFNbkIsVUFBVTs7Ozs7Ozs7Ozs7O0FBS25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9jb21wb25lbnRzL1RlbmFudE1hbmFnZW1lbnQvVGVuYW50U3dpdGNoZXIudHN4P2U1MTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRlbmFudCB9IGZyb20gJ0AvY29udGV4dHMvVGVuYW50Q29udGV4dCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBcbiAgQ2hldnJvbkRvd25JY29uLFxuICBCdWlsZGluZ09mZmljZUljb24sXG4gIFBsdXNJY29uLFxuICBDaGVja0ljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuaW50ZXJmYWNlIFRlbmFudFN3aXRjaGVyUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRlbmFudFN3aXRjaGVyKHsgY2xhc3NOYW1lID0gJycgfTogVGVuYW50U3dpdGNoZXJQcm9wcykge1xuICBjb25zdCB7IHRlbmFudCwgdGVuYW50SWQsIGxvYWRpbmcsIHN3aXRjaFRlbmFudCwgY3JlYXRlVGVuYW50IH0gPSB1c2VUZW5hbnQoKTtcbiAgY29uc3QgeyBwcm9maWxlIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtpc09wZW4sIHNldElzT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0NyZWF0aW5nLCBzZXRJc0NyZWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW25ld1RlbmFudE5hbWUsIHNldE5ld1RlbmFudE5hbWVdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIEZvciBkZW1vIHB1cnBvc2VzLCB3ZSdsbCBzaG93IGF2YWlsYWJsZSB0ZW5hbnRzXG4gIC8vIEluIGEgcmVhbCBhcHAsIHlvdSBtaWdodCBmZXRjaCB0aGlzIGZyb20gYSB1c2VyJ3MgYWNjZXNzaWJsZSB0ZW5hbnRzXG4gIGNvbnN0IGF2YWlsYWJsZVRlbmFudHMgPSB0ZW5hbnRJZCA/IFtcbiAgICB7IGlkOiB0ZW5hbnRJZCwgbmFtZTogdGVuYW50Py5uYW1lIHx8ICdDdXJyZW50IENsaW5pYycgfSxcbiAgICAvLyBBZGQgbW9yZSB0ZW5hbnRzIGhlcmUgaWYgdXNlciBoYXMgYWNjZXNzIHRvIG11bHRpcGxlXG4gIF0gOiBbXTtcblxuICBjb25zdCBoYW5kbGVTd2l0Y2hUZW5hbnQgPSBhc3luYyAobmV3VGVuYW50SWQ6IHN0cmluZykgPT4ge1xuICAgIGlmIChuZXdUZW5hbnRJZCA9PT0gdGVuYW50SWQpIHtcbiAgICAgIHNldElzT3BlbihmYWxzZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHN3aXRjaFRlbmFudChuZXdUZW5hbnRJZCk7XG4gICAgICBzZXRJc09wZW4oZmFsc2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3dpdGNoIHRlbmFudDonLCBlcnJvcik7XG4gICAgICAvLyBZb3UgbWlnaHQgd2FudCB0byBzaG93IGEgdG9hc3Qgbm90aWZpY2F0aW9uIGhlcmVcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlVGVuYW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghbmV3VGVuYW50TmFtZS50cmltKCkpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0NyZWF0aW5nKHRydWUpO1xuICAgICAgYXdhaXQgY3JlYXRlVGVuYW50KHtcbiAgICAgICAgbmFtZTogbmV3VGVuYW50TmFtZS50cmltKCksXG4gICAgICAgIGFkZHJlc3M6ICcnLFxuICAgICAgICBwaG9uZTogJycsXG4gICAgICAgIGVtYWlsOiBwcm9maWxlPy5lbWFpbCB8fCAnJ1xuICAgICAgfSk7XG4gICAgICBzZXROZXdUZW5hbnROYW1lKCcnKTtcbiAgICAgIHNldElzT3BlbihmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjcmVhdGUgdGVuYW50OicsIGVycm9yKTtcbiAgICAgIC8vIFlvdSBtaWdodCB3YW50IHRvIHNob3cgYSB0b2FzdCBub3RpZmljYXRpb24gaGVyZVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0NyZWF0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgJHtjbGFzc05hbWV9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmF5LTIwMCByb3VuZGVkLWxnIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMyIGgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcmVsYXRpdmUgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzT3BlbighaXNPcGVuKX1cbiAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHctZnVsbCBweC0zIHB5LTIgdGV4dC1sZWZ0IGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LTUwMCBmb2N1czpib3JkZXItcHJpbWFyeS01MDBcIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICA8QnVpbGRpbmdPZmZpY2VJY29uIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICB7dGVuYW50Py5uYW1lIHx8ICdTZWxlY3QgQ2xpbmljJ31cbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICB7dGVuYW50Py5hZGRyZXNzIHx8ICdObyBhZGRyZXNzIHNldCd9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPENoZXZyb25Eb3duSWNvbiBcbiAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00IHRleHQtZ3JheS00MDAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gJHtcbiAgICAgICAgICAgIGlzT3BlbiA/ICd0cmFuc2Zvcm0gcm90YXRlLTE4MCcgOiAnJ1xuICAgICAgICAgIH1gfSBcbiAgICAgICAgLz5cbiAgICAgIDwvYnV0dG9uPlxuXG4gICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB6LTUwIHctZnVsbCBtdC0xIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTFcIj5cbiAgICAgICAgICAgIHthdmFpbGFibGVUZW5hbnRzLm1hcCgoYXZhaWxhYmxlVGVuYW50KSA9PiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2F2YWlsYWJsZVRlbmFudC5pZH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTd2l0Y2hUZW5hbnQoYXZhaWxhYmxlVGVuYW50LmlkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtMyBweS0yIHRleHQtbGVmdCBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2F2YWlsYWJsZVRlbmFudC5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHthdmFpbGFibGVUZW5hbnQuaWQgPT09IHRlbmFudElkICYmIChcbiAgICAgICAgICAgICAgICAgIDxDaGVja0ljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXByaW1hcnktNjAwXCIgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTEwMCBtdC0xIHB0LTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0zIHB5LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJOZXcgY2xpbmljIG5hbWVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3VGVuYW50TmFtZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdUZW5hbnROYW1lKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTIgcHktMSB0ZXh0LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0xIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXByaW1hcnktNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgb25LZXlQcmVzcz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNyZWF0ZVRlbmFudCgpO1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNyZWF0ZVRlbmFudH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFuZXdUZW5hbnROYW1lLnRyaW0oKSB8fCBpc0NyZWF0aW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LTggaC04IHRleHQtd2hpdGUgYmctcHJpbWFyeS02MDAgcm91bmRlZCBob3ZlcjpiZy1wcmltYXJ5LTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXNDcmVhdGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYm9yZGVyLTIgYm9yZGVyLXdoaXRlIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8UGx1c0ljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgQ3JlYXRlIGEgbmV3IGNsaW5pY1xuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogT3ZlcmxheSB0byBjbG9zZSBkcm9wZG93biB3aGVuIGNsaWNraW5nIG91dHNpZGUgKi99XG4gICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgPGRpdiBcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNDBcIiBcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oZmFsc2UpfVxuICAgICAgICAvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVRlbmFudCIsInVzZUF1dGgiLCJDaGV2cm9uRG93bkljb24iLCJCdWlsZGluZ09mZmljZUljb24iLCJQbHVzSWNvbiIsIkNoZWNrSWNvbiIsIlRlbmFudFN3aXRjaGVyIiwiY2xhc3NOYW1lIiwidGVuYW50IiwidGVuYW50SWQiLCJsb2FkaW5nIiwic3dpdGNoVGVuYW50IiwiY3JlYXRlVGVuYW50IiwicHJvZmlsZSIsImlzT3BlbiIsInNldElzT3BlbiIsImlzQ3JlYXRpbmciLCJzZXRJc0NyZWF0aW5nIiwibmV3VGVuYW50TmFtZSIsInNldE5ld1RlbmFudE5hbWUiLCJhdmFpbGFibGVUZW5hbnRzIiwiaWQiLCJuYW1lIiwiaGFuZGxlU3dpdGNoVGVuYW50IiwibmV3VGVuYW50SWQiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVDcmVhdGVUZW5hbnQiLCJ0cmltIiwiYWRkcmVzcyIsInBob25lIiwiZW1haWwiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwicCIsIm1hcCIsImF2YWlsYWJsZVRlbmFudCIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvbktleVByZXNzIiwia2V5IiwiZGlzYWJsZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TenantManagement/TenantSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, async (user)=>{\n            setUser(user);\n            if (user) {\n                try {\n                    // Fetch user profile from Firestore\n                    const profileDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid));\n                    if (profileDoc.exists()) {\n                        const profileData = profileDoc.data();\n                        setProfile({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            ...profileData\n                        });\n                    } else {\n                        // Create default profile if doesn't exist\n                        const defaultProfile = {\n                            id: user.uid,\n                            name: user.displayName || \"User\",\n                            email: user.email || \"\",\n                            role: \"receptionist\",\n                            tenantId: `tenant_${user.uid}`,\n                            permissions: [\n                                \"read_patients\",\n                                \"manage_appointments\"\n                            ],\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), defaultProfile);\n                        setProfile(defaultProfile);\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user profile:\", error);\n                }\n            } else {\n                setProfile(null);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, []);\n    const signIn = async (email, password)=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const signUp = async (email, password, profileData)=>{\n        try {\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n            const user = userCredential.user;\n            // Create user profile in Firestore\n            const newProfile = {\n                id: user.uid,\n                email: user.email || email,\n                ...profileData,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), newProfile);\n            setProfile(newProfile);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user || !profile) throw new Error(\"No user logged in\");\n        try {\n            const updatedProfile = {\n                ...profile,\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", user.uid), updatedProfile, {\n                merge: true\n            });\n            setProfile(updatedProfile);\n        } catch (error) {\n            throw new Error(error.message);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            profile,\n            loading,\n            signIn,\n            signUp,\n            logout,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/TenantContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/TenantContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ TenantProvider,useTenant auto */ \n\n\n\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction TenantProvider({ children }) {\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tenantId, setTenantId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { user, profile } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadTenant = async ()=>{\n            if (!user || !profile?.tenantId) {\n                setTenant(null);\n                setTenantId(null);\n                setLoading(false);\n                return;\n            }\n            try {\n                const tenantDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", profile.tenantId, \"settings\", \"clinic\"));\n                if (tenantDoc.exists()) {\n                    const tenantData = tenantDoc.data();\n                    setTenant({\n                        ...tenantData,\n                        id: profile.tenantId\n                    });\n                    setTenantId(profile.tenantId);\n                } else {\n                    // Create default tenant settings if they don't exist\n                    const defaultTenant = {\n                        id: profile.tenantId,\n                        name: \"Klinik Gigi\",\n                        address: \"\",\n                        phone: \"\",\n                        email: profile.email,\n                        settings: {\n                            timezone: \"Asia/Jakarta\",\n                            currency: \"IDR\",\n                            dateFormat: \"DD/MM/YYYY\",\n                            businessHours: {\n                                start: \"08:00\",\n                                end: \"17:00\",\n                                days: [\n                                    \"monday\",\n                                    \"tuesday\",\n                                    \"wednesday\",\n                                    \"thursday\",\n                                    \"friday\",\n                                    \"saturday\"\n                                ]\n                            }\n                        },\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", profile.tenantId, \"settings\", \"clinic\"), defaultTenant);\n                    setTenant(defaultTenant);\n                    setTenantId(profile.tenantId);\n                }\n            } catch (error) {\n                console.error(\"Error loading tenant:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadTenant();\n    }, [\n        user,\n        profile\n    ]);\n    const switchTenant = async (newTenantId)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            setLoading(true);\n            // Update user's tenantId in global users collection\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.uid), {\n                tenantId: newTenantId,\n                updatedAt: new Date().toISOString()\n            }, {\n                merge: true\n            });\n            // Load new tenant data\n            const tenantDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", newTenantId, \"settings\", \"clinic\"));\n            if (tenantDoc.exists()) {\n                const tenantData = tenantDoc.data();\n                setTenant({\n                    ...tenantData,\n                    id: newTenantId\n                });\n                setTenantId(newTenantId);\n            }\n        } catch (error) {\n            console.error(\"Error switching tenant:\", error);\n            throw new Error(\"Failed to switch tenant\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateTenant = async (updates)=>{\n        if (!tenantId || !tenant) throw new Error(\"No tenant selected\");\n        try {\n            const updatedTenant = {\n                ...tenant,\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", tenantId, \"settings\", \"clinic\"), updatedTenant, {\n                merge: true\n            });\n            setTenant(updatedTenant);\n        } catch (error) {\n            console.error(\"Error updating tenant:\", error);\n            throw new Error(\"Failed to update tenant\");\n        }\n    };\n    const createTenant = async (tenantData)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            // Generate tenant ID (could be UUID or custom format)\n            const newTenantId = `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTenant = {\n                ...tenantData,\n                id: newTenantId,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // Create tenant settings\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"dentalcare\", newTenantId, \"settings\", \"clinic\"), newTenant);\n            // Update user's tenantId\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.uid), {\n                tenantId: newTenantId,\n                updatedAt: new Date().toISOString()\n            }, {\n                merge: true\n            });\n            setTenant(newTenant);\n            setTenantId(newTenantId);\n            return newTenantId;\n        } catch (error) {\n            console.error(\"Error creating tenant:\", error);\n            throw new Error(\"Failed to create tenant\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            tenantId,\n            loading,\n            switchTenant,\n            updateTenant,\n            createTenant\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\contexts\\\\TenantContext.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\nconst useTenant = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (!context) {\n        throw new Error(\"useTenant must be used within TenantProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/TenantContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAppointments.ts":
/*!**************************************!*\
  !*** ./src/hooks/useAppointments.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppointment: () => (/* binding */ useAppointment),\n/* harmony export */   useAppointments: () => (/* binding */ useAppointments),\n/* harmony export */   useAppointmentsByDate: () => (/* binding */ useAppointmentsByDate),\n/* harmony export */   useAppointmentsByDoctor: () => (/* binding */ useAppointmentsByDoctor),\n/* harmony export */   useAppointmentsByPatient: () => (/* binding */ useAppointmentsByPatient),\n/* harmony export */   useCreateAppointment: () => (/* binding */ useCreateAppointment),\n/* harmony export */   useDeleteAppointment: () => (/* binding */ useDeleteAppointment),\n/* harmony export */   useRealTimeAppointments: () => (/* binding */ useRealTimeAppointments),\n/* harmony export */   useRealTimeTodayAppointments: () => (/* binding */ useRealTimeTodayAppointments),\n/* harmony export */   useTodayAppointments: () => (/* binding */ useTodayAppointments),\n/* harmony export */   useUpcomingAppointments: () => (/* binding */ useUpcomingAppointments),\n/* harmony export */   useUpdateAppointment: () => (/* binding */ useUpdateAppointment),\n/* harmony export */   useUpdateAppointmentStatus: () => (/* binding */ useUpdateAppointmentStatus)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_appointments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/appointments */ \"(ssr)/./src/services/appointments.ts\");\n/* harmony import */ var _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/base/TenantService */ \"(ssr)/./src/services/base/TenantService.ts\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n\n\n\n\n\n/**\n * Get all appointments for current tenant\n */ function useAppointments() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointments\",\n            tenantId\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getAppointments();\n        },\n        enabled: !!tenantId,\n        staleTime: 2 * 60 * 1000\n    });\n}\n/**\n * Get single appointment by ID\n */ function useAppointment(id) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointment\",\n            tenantId,\n            id\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getAppointment(id);\n        },\n        enabled: !!id && !!tenantId\n    });\n}\n/**\n * Get appointments by date\n */ function useAppointmentsByDate(date) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointments\",\n            \"date\",\n            tenantId,\n            date\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getAppointmentsByDate(date);\n        },\n        enabled: !!tenantId && !!date,\n        staleTime: 1 * 60 * 1000\n    });\n}\n/**\n * Get today's appointments\n */ function useTodayAppointments() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    const today = new Date().toISOString().split(\"T\")[0];\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointments\",\n            \"today\",\n            tenantId,\n            today\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getTodayAppointments();\n        },\n        enabled: !!tenantId,\n        staleTime: 1 * 60 * 1000,\n        refetchInterval: 2 * 60 * 1000\n    });\n}\n/**\n * Get appointments by patient ID\n */ function useAppointmentsByPatient(patientId) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointments\",\n            \"patient\",\n            tenantId,\n            patientId\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getAppointmentsByPatient(patientId);\n        },\n        enabled: !!tenantId && !!patientId,\n        staleTime: 5 * 60 * 1000\n    });\n}\n/**\n * Get appointments by doctor ID\n */ function useAppointmentsByDoctor(doctorId) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointments\",\n            \"doctor\",\n            tenantId,\n            doctorId\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getAppointmentsByDoctor(doctorId);\n        },\n        enabled: !!tenantId && !!doctorId,\n        staleTime: 5 * 60 * 1000\n    });\n}\n/**\n * Get upcoming appointments\n */ function useUpcomingAppointments(days = 7) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"appointments\",\n            \"upcoming\",\n            tenantId,\n            days\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.getUpcomingAppointments(days);\n        },\n        enabled: !!tenantId,\n        staleTime: 5 * 60 * 1000\n    });\n}\n/**\n * Create new appointment\n */ function useCreateAppointment() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: (appointmentData)=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.createAppointment(appointmentData);\n        },\n        onSuccess: (_, appointmentData)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"date\",\n                    tenantId,\n                    appointmentData.date\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"today\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"upcoming\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"patient\",\n                    tenantId,\n                    appointmentData.patientId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"doctor\",\n                    tenantId,\n                    appointmentData.doctorId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating appointment:\", error);\n        }\n    });\n}\n/**\n * Update appointment\n */ function useUpdateAppointment() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: ({ id, updates })=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.updateAppointment(id, updates);\n        },\n        onSuccess: (_, { id, updates })=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointment\",\n                    tenantId,\n                    id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"today\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"upcoming\",\n                    tenantId\n                ]\n            });\n            // Invalidate date-specific queries if date was updated\n            if (updates.date) {\n                queryClient.invalidateQueries({\n                    queryKey: [\n                        \"appointments\",\n                        \"date\",\n                        tenantId,\n                        updates.date\n                    ]\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating appointment:\", error);\n        }\n    });\n}\n/**\n * Update appointment status\n */ function useUpdateAppointmentStatus() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: ({ id, status, notes })=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.updateAppointmentStatus(id, status, notes);\n        },\n        onSuccess: (_, { id })=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointment\",\n                    tenantId,\n                    id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"today\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating appointment status:\", error);\n        }\n    });\n}\n/**\n * Delete appointment\n */ function useDeleteAppointment() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: (id)=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n            return appointmentService.deleteAppointment(id);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"today\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"appointments\",\n                    \"upcoming\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting appointment:\", error);\n        }\n    });\n}\n/**\n * Real-time appointments subscription\n */ function useRealTimeAppointments() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!tenantId) {\n            setLoading(false);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n        const unsubscribe = appointmentService.subscribeToAppointments((updatedAppointments)=>{\n            setAppointments(updatedAppointments);\n            setLoading(false);\n        });\n        return ()=>{\n            unsubscribe();\n        };\n    }, [\n        tenantId\n    ]);\n    return {\n        appointments,\n        loading,\n        error\n    };\n}\n/**\n * Real-time today's appointments subscription\n */ function useRealTimeTodayAppointments() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!tenantId) {\n            setLoading(false);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        const appointmentService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_appointments__WEBPACK_IMPORTED_MODULE_1__.AppointmentService, tenantId, \"appointments\");\n        const unsubscribe = appointmentService.subscribeToTodayAppointments((updatedAppointments)=>{\n            setAppointments(updatedAppointments);\n            setLoading(false);\n        });\n        return ()=>{\n            unsubscribe();\n        };\n    }, [\n        tenantId\n    ]);\n    return {\n        appointments,\n        loading,\n        error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAppointments.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useDashboard.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDashboard.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRealTimeDashboard: () => (/* binding */ useRealTimeDashboard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n\n\n\n\nfunction useRealTimeDashboard() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        todayAppointments: 0,\n        todayRevenue: 0,\n        totalPatients: 0,\n        monthlyRevenue: 0,\n        pendingPayments: 0,\n        lowStockItems: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!tenantId) return;\n        const today = new Date().toISOString().split(\"T\")[0];\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        // Subscribe to today's appointments\n        const appointmentsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", tenantId, \"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"date\", \"==\", today));\n        const unsubscribeAppointments = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.onSnapshot)(appointmentsQuery, (snapshot)=>{\n            setStats((prev)=>({\n                    ...prev,\n                    todayAppointments: snapshot.size\n                }));\n        });\n        // Subscribe to patients count\n        const patientsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", tenantId, \"patients\"));\n        const unsubscribePatients = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.onSnapshot)(patientsQuery, (snapshot)=>{\n            setStats((prev)=>({\n                    ...prev,\n                    totalPatients: snapshot.size\n                }));\n        });\n        // Subscribe to pending invoices\n        const invoicesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", tenantId, \"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"status\", \"in\", [\n            \"draft\",\n            \"sent\",\n            \"overdue\"\n        ]));\n        const unsubscribeInvoices = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.onSnapshot)(invoicesQuery, (snapshot)=>{\n            setStats((prev)=>({\n                    ...prev,\n                    pendingPayments: snapshot.size\n                }));\n        });\n        // Get monthly revenue (one-time fetch for now)\n        const fetchMonthlyRevenue = async ()=>{\n            try {\n                const monthlyInvoicesQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", tenantId, \"invoices\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"status\", \"==\", \"paid\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"date\", \">=\", currentMonth + \"-01\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"date\", \"<\", getNextMonth(currentMonth) + \"-01\"));\n                const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(monthlyInvoicesQuery);\n                let totalRevenue = 0;\n                let todayRevenue = 0;\n                snapshot.docs.forEach((doc)=>{\n                    const invoice = doc.data();\n                    totalRevenue += invoice.total || 0;\n                    if (invoice.date === today) {\n                        todayRevenue += invoice.total || 0;\n                    }\n                });\n                setStats((prev)=>({\n                        ...prev,\n                        monthlyRevenue: totalRevenue,\n                        todayRevenue: todayRevenue\n                    }));\n            } catch (error) {\n                console.error(\"Error fetching monthly revenue:\", error);\n            }\n        };\n        fetchMonthlyRevenue();\n        // Subscribe to inventory for low stock items\n        const inventoryQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"dentalcare\", tenantId, \"inventory\"));\n        const unsubscribeInventory = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.onSnapshot)(inventoryQuery, (snapshot)=>{\n            let lowStockCount = 0;\n            snapshot.docs.forEach((doc)=>{\n                const item = doc.data();\n                if (item.currentStock <= item.minStock) {\n                    lowStockCount++;\n                }\n            });\n            setStats((prev)=>({\n                    ...prev,\n                    lowStockItems: lowStockCount\n                }));\n        });\n        return ()=>{\n            unsubscribeAppointments();\n            unsubscribePatients();\n            unsubscribeInvoices();\n            unsubscribeInventory();\n        };\n    }, [\n        tenantId\n    ]);\n    return stats;\n}\nfunction getNextMonth(currentMonth) {\n    const [year, month] = currentMonth.split(\"-\").map(Number);\n    const nextMonth = month === 12 ? 1 : month + 1;\n    const nextYear = month === 12 ? year + 1 : year;\n    return `${nextYear}-${nextMonth.toString().padStart(2, \"0\")}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useDashboard.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/usePatients.ts":
/*!**********************************!*\
  !*** ./src/hooks/usePatients.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAddClinicalImage: () => (/* binding */ useAddClinicalImage),\n/* harmony export */   useCreatePatient: () => (/* binding */ useCreatePatient),\n/* harmony export */   useDeletePatient: () => (/* binding */ useDeletePatient),\n/* harmony export */   usePatient: () => (/* binding */ usePatient),\n/* harmony export */   usePatients: () => (/* binding */ usePatients),\n/* harmony export */   useRealTimePatients: () => (/* binding */ useRealTimePatients),\n/* harmony export */   useSearchPatients: () => (/* binding */ useSearchPatients),\n/* harmony export */   useUpdateDentalChart: () => (/* binding */ useUpdateDentalChart),\n/* harmony export */   useUpdatePatient: () => (/* binding */ useUpdatePatient)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_patients__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/patients */ \"(ssr)/./src/services/patients.ts\");\n/* harmony import */ var _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/base/TenantService */ \"(ssr)/./src/services/base/TenantService.ts\");\n/* harmony import */ var _contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/TenantContext */ \"(ssr)/./src/contexts/TenantContext.tsx\");\n\n\n\n\n\n/**\n * Get all patients for current tenant\n */ function usePatients() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"patients\",\n            tenantId\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.getPatients();\n        },\n        enabled: !!tenantId,\n        staleTime: 5 * 60 * 1000\n    });\n}\n/**\n * Get single patient by ID\n */ function usePatient(id) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"patient\",\n            tenantId,\n            id\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.getPatient(id);\n        },\n        enabled: !!id && !!tenantId\n    });\n}\n/**\n * Search patients\n */ function useSearchPatients(searchTerm) {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: [\n            \"patients\",\n            \"search\",\n            tenantId,\n            searchTerm\n        ],\n        queryFn: ()=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.searchPatients(searchTerm);\n        },\n        enabled: !!tenantId && searchTerm.length >= 2,\n        staleTime: 2 * 60 * 1000\n    });\n}\n/**\n * Create new patient\n */ function useCreatePatient() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: (patientData)=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.createPatient(patientData);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patients\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating patient:\", error);\n        }\n    });\n}\n/**\n * Update patient\n */ function useUpdatePatient() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: ({ id, updates })=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.updatePatient(id, updates);\n        },\n        onSuccess: (_, { id })=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patients\",\n                    tenantId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patient\",\n                    tenantId,\n                    id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating patient:\", error);\n        }\n    });\n}\n/**\n * Delete patient\n */ function useDeletePatient() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: (id)=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.deletePatient(id);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patients\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error deleting patient:\", error);\n        }\n    });\n}\n/**\n * Add clinical image to patient\n */ function useAddClinicalImage() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: ({ patientId, imageData })=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.addClinicalImage(patientId, imageData);\n        },\n        onSuccess: (_, { patientId })=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patient\",\n                    tenantId,\n                    patientId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patients\",\n                    tenantId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error adding clinical image:\", error);\n        }\n    });\n}\n/**\n * Update dental chart\n */ function useUpdateDentalChart() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQueryClient)();\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: ({ patientId, toothNumber, condition, notes })=>{\n            if (!tenantId) throw new Error(\"No tenant selected\");\n            const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n            return patientService.updateDentalChart(patientId, toothNumber, condition, notes);\n        },\n        onSuccess: (_, { patientId })=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"patient\",\n                    tenantId,\n                    patientId\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dental chart:\", error);\n        }\n    });\n}\n/**\n * Real-time patients subscription\n */ function useRealTimePatients() {\n    const { tenantId } = (0,_contexts_TenantContext__WEBPACK_IMPORTED_MODULE_3__.useTenant)();\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!tenantId) {\n            setLoading(false);\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        const patientService = _services_base_TenantService__WEBPACK_IMPORTED_MODULE_2__.TenantServiceRegistry.getService(_services_patients__WEBPACK_IMPORTED_MODULE_1__.PatientService, tenantId, \"patients\");\n        const unsubscribe = patientService.subscribeToPatients((updatedPatients)=>{\n            setPatients(updatedPatients);\n            setLoading(false);\n        });\n        return ()=>{\n            unsubscribe();\n        };\n    }, [\n        tenantId\n    ]);\n    return {\n        patients,\n        loading,\n        error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/usePatients.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDv9u4NhGvNFVVsou_IrXMO__rlfXfCKIk\",\n    authDomain: \"widigital-d6110.firebaseapp.com\",\n    projectId: \"widigital-d6110\",\n    storageBucket: \"widigital-d6110.firebasestorage.app\",\n    messagingSenderId: \"329879577024\",\n    appId: \"1:329879577024:web:0d8752f8175569f67d6825\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\n// Connect to emulators in development (disabled for now)\n// if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {\n//   try {\n//     // Only connect if not already connected\n//     if (!auth.config.emulator) {\n//       connectAuthEmulator(auth, 'http://localhost:9099');\n//     }\n//     // @ts-ignore\n//     if (!db._delegate._databaseId.projectId.includes('localhost')) {\n//       connectFirestoreEmulator(db, 'localhost', 8080);\n//     }\n//   } catch (error) {\n//     console.log('Emulators already connected or not available');\n//   }\n// }\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/queryClient.ts":
/*!********************************!*\
  !*** ./src/lib/queryClient.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryClient: () => (/* binding */ queryClient)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 5 * 60 * 1000,\n            gcTime: 10 * 60 * 1000,\n            retry: (failureCount, error)=>{\n                // Don't retry on auth errors\n                if (error?.code === \"permission-denied\" || error?.code === \"unauthenticated\") {\n                    return false;\n                }\n                return failureCount < 3;\n            },\n            refetchOnWindowFocus: false\n        },\n        mutations: {\n            retry: false\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3F1ZXJ5Q2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBRTdDLE1BQU1DLGNBQWMsSUFBSUQsOERBQVdBLENBQUM7SUFDekNFLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLFdBQVcsSUFBSSxLQUFLO1lBQ3BCQyxRQUFRLEtBQUssS0FBSztZQUNsQkMsT0FBTyxDQUFDQyxjQUFjQztnQkFDcEIsNkJBQTZCO2dCQUM3QixJQUFJQSxPQUFPQyxTQUFTLHVCQUF1QkQsT0FBT0MsU0FBUyxtQkFBbUI7b0JBQzVFLE9BQU87Z0JBQ1Q7Z0JBQ0EsT0FBT0YsZUFBZTtZQUN4QjtZQUNBRyxzQkFBc0I7UUFDeEI7UUFDQUMsV0FBVztZQUNUTCxPQUFPO1FBQ1Q7SUFDRjtBQUNGLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW50YWwtY2xpbmljLXVpLy4vc3JjL2xpYi9xdWVyeUNsaWVudC50cz81MWIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFF1ZXJ5Q2xpZW50IH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcblxuZXhwb3J0IGNvbnN0IHF1ZXJ5Q2xpZW50ID0gbmV3IFF1ZXJ5Q2xpZW50KHtcbiAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICBxdWVyaWVzOiB7XG4gICAgICBzdGFsZVRpbWU6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xuICAgICAgZ2NUaW1lOiAxMCAqIDYwICogMTAwMCwgLy8gMTAgbWludXRlcyAod2FzIGNhY2hlVGltZSlcbiAgICAgIHJldHJ5OiAoZmFpbHVyZUNvdW50LCBlcnJvcjogYW55KSA9PiB7XG4gICAgICAgIC8vIERvbid0IHJldHJ5IG9uIGF1dGggZXJyb3JzXG4gICAgICAgIGlmIChlcnJvcj8uY29kZSA9PT0gJ3Blcm1pc3Npb24tZGVuaWVkJyB8fCBlcnJvcj8uY29kZSA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhaWx1cmVDb3VudCA8IDM7XG4gICAgICB9LFxuICAgICAgcmVmZXRjaE9uV2luZG93Rm9jdXM6IGZhbHNlLFxuICAgIH0sXG4gICAgbXV0YXRpb25zOiB7XG4gICAgICByZXRyeTogZmFsc2UsXG4gICAgfSxcbiAgfSxcbn0pO1xuIl0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50IiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJnY1RpbWUiLCJyZXRyeSIsImZhaWx1cmVDb3VudCIsImVycm9yIiwiY29kZSIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwibXV0YXRpb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/queryClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/appointments.ts":
/*!**************************************!*\
  !*** ./src/services/appointments.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentService: () => (/* binding */ AppointmentService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _base_TenantService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base/TenantService */ \"(ssr)/./src/services/base/TenantService.ts\");\n\n\nclass AppointmentService extends _base_TenantService__WEBPACK_IMPORTED_MODULE_1__.TenantService {\n    /**\n   * Create new appointment\n   */ async createAppointment(appointmentData) {\n        try {\n            this.validateTenantAccess();\n            const newAppointment = {\n                ...appointmentData,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)(this.getCollection(\"appointments\"), newAppointment);\n            return docRef.id;\n        } catch (error) {\n            this.handleError(error, \"create appointment\");\n        }\n    }\n    /**\n   * Get appointment by ID\n   */ async getAppointment(id) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"appointments\", id);\n            const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n            if (docSnap.exists()) {\n                return {\n                    id: docSnap.id,\n                    ...docSnap.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            this.handleError(error, \"get appointment\");\n        }\n    }\n    /**\n   * Get all appointments for current tenant\n   */ async getAppointments() {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"time\", \"asc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get appointments\");\n        }\n    }\n    /**\n   * Get appointments by date\n   */ async getAppointmentsByDate(date) {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \"==\", date), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"time\", \"asc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get appointments by date\");\n        }\n    }\n    /**\n   * Get appointments by patient ID\n   */ async getAppointmentsByPatient(patientId) {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"patientId\", \"==\", patientId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get appointments by patient\");\n        }\n    }\n    /**\n   * Get appointments by doctor ID\n   */ async getAppointmentsByDoctor(doctorId) {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"doctorId\", \"==\", doctorId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get appointments by doctor\");\n        }\n    }\n    /**\n   * Update appointment\n   */ async updateAppointment(id, updates) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"appointments\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(docRef, {\n                ...updates,\n                updatedAt: new Date().toISOString()\n            });\n        } catch (error) {\n            this.handleError(error, \"update appointment\");\n        }\n    }\n    /**\n   * Delete appointment\n   */ async deleteAppointment(id) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"appointments\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)(docRef);\n        } catch (error) {\n            this.handleError(error, \"delete appointment\");\n        }\n    }\n    /**\n   * Update appointment status\n   */ async updateAppointmentStatus(id, status, notes) {\n        try {\n            this.validateTenantAccess();\n            const updates = {\n                status,\n                updatedAt: new Date().toISOString()\n            };\n            if (notes) {\n                updates.notes = notes;\n            }\n            await this.updateAppointment(id, updates);\n        } catch (error) {\n            this.handleError(error, \"update appointment status\");\n        }\n    }\n    /**\n   * Get today's appointments\n   */ async getTodayAppointments() {\n        const today = new Date().toISOString().split(\"T\")[0];\n        return this.getAppointmentsByDate(today);\n    }\n    /**\n   * Get upcoming appointments\n   */ async getUpcomingAppointments(days = 7) {\n        try {\n            this.validateTenantAccess();\n            const today = new Date();\n            const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \">=\", today.toISOString().split(\"T\")[0]), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \"<=\", futureDate.toISOString().split(\"T\")[0]), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"asc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"time\", \"asc\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get upcoming appointments\");\n        }\n    }\n    /**\n   * Real-time subscription to appointments\n   */ subscribeToAppointments(callback) {\n        this.validateTenantAccess();\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"date\", \"desc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"time\", \"asc\"));\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)(q, (snapshot)=>{\n            const appointments = snapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            callback(appointments);\n        }, (error)=>{\n            console.error(\"Error in appointments subscription:\", error);\n        });\n    }\n    /**\n   * Subscribe to today's appointments\n   */ subscribeToTodayAppointments(callback) {\n        this.validateTenantAccess();\n        const today = new Date().toISOString().split(\"T\")[0];\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"appointments\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"date\", \"==\", today), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"time\", \"asc\"));\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)(q, (snapshot)=>{\n            const appointments = snapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            callback(appointments);\n        }, (error)=>{\n            console.error(\"Error in today appointments subscription:\", error);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/appointments.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/base/TenantService.ts":
/*!********************************************!*\
  !*** ./src/services/base/TenantService.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantService: () => (/* binding */ TenantService),\n/* harmony export */   TenantServiceRegistry: () => (/* binding */ TenantServiceRegistry),\n/* harmony export */   createTenantService: () => (/* binding */ createTenantService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n/**\n * Base service class for tenant-aware Firestore operations\n * All tenant-specific services should extend this class\n */ class TenantService {\n    constructor(tenantId){\n        if (!tenantId) {\n            throw new Error(\"TenantService requires a valid tenantId\");\n        }\n        this.tenantId = tenantId;\n    }\n    /**\n   * Get a tenant-specific collection reference\n   */ getCollection(collectionName) {\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"dentalcare\", this.tenantId, collectionName);\n    }\n    /**\n   * Get a tenant-specific document reference\n   */ getDocument(collectionName, documentId) {\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"dentalcare\", this.tenantId, collectionName, documentId);\n    }\n    /**\n   * Get tenant settings document reference\n   */ getSettingsDocument(settingType) {\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"dentalcare\", this.tenantId, \"settings\", settingType);\n    }\n    /**\n   * Validate tenant access (can be overridden by subclasses)\n   */ validateTenantAccess() {\n        if (!this.tenantId) {\n            throw new Error(\"No tenant ID provided\");\n        }\n    }\n    /**\n   * Handle Firestore errors with tenant context\n   */ handleError(error, operation) {\n        console.error(`TenantService Error [${this.tenantId}] - ${operation}:`, error);\n        if (error instanceof firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.FirestoreError) {\n            switch(error.code){\n                case \"permission-denied\":\n                    throw new Error(`Access denied for tenant ${this.tenantId}. Please check your permissions.`);\n                case \"not-found\":\n                    throw new Error(`Resource not found in tenant ${this.tenantId}.`);\n                case \"unavailable\":\n                    throw new Error(\"Service temporarily unavailable. Please try again.\");\n                default:\n                    throw new Error(`Database operation failed: ${error.message}`);\n            }\n        }\n        throw new Error(`Failed to ${operation}: ${error.message || \"Unknown error\"}`);\n    }\n    /**\n   * Get current tenant ID\n   */ getTenantId() {\n        return this.tenantId;\n    }\n    /**\n   * Update tenant ID (useful for tenant switching)\n   */ setTenantId(newTenantId) {\n        if (!newTenantId) {\n            throw new Error(\"Invalid tenant ID\");\n        }\n        this.tenantId = newTenantId;\n    }\n}\n/**\n * Factory function to create tenant-aware service instances\n */ function createTenantService(ServiceClass, tenantId) {\n    return new ServiceClass(tenantId);\n}\n/**\n * Service registry for managing tenant-specific service instances\n */ class TenantServiceRegistry {\n    static{\n        this.instances = new Map();\n    }\n    /**\n   * Get or create a tenant-specific service instance\n   */ static getService(ServiceClass, tenantId, serviceName) {\n        if (!this.instances.has(tenantId)) {\n            this.instances.set(tenantId, new Map());\n        }\n        const tenantServices = this.instances.get(tenantId);\n        if (!tenantServices.has(serviceName)) {\n            tenantServices.set(serviceName, new ServiceClass(tenantId));\n        }\n        return tenantServices.get(serviceName);\n    }\n    /**\n   * Clear all service instances for a tenant (useful for tenant switching)\n   */ static clearTenantServices(tenantId) {\n        this.instances.delete(tenantId);\n    }\n    /**\n   * Clear all service instances\n   */ static clearAllServices() {\n        this.instances.clear();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/base/TenantService.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/patients.ts":
/*!**********************************!*\
  !*** ./src/services/patients.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PatientService: () => (/* binding */ PatientService)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _base_TenantService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base/TenantService */ \"(ssr)/./src/services/base/TenantService.ts\");\n\n\nclass PatientService extends _base_TenantService__WEBPACK_IMPORTED_MODULE_1__.TenantService {\n    /**\n   * Generate unique medical record number\n   */ async generateMedicalRecordNumber() {\n        const year = new Date().getFullYear();\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"patients\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"medicalRecordNumber\", \">=\", `RM${year}`), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"medicalRecordNumber\", \"<\", `RM${year + 1}`), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"medicalRecordNumber\", \"desc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (snapshot.empty) {\n            return `RM${year}001`;\n        }\n        const lastRecord = snapshot.docs[0].data();\n        const lastNumber = parseInt(lastRecord.medicalRecordNumber.slice(-3));\n        const nextNumber = (lastNumber + 1).toString().padStart(3, \"0\");\n        return `RM${year}${nextNumber}`;\n    }\n    /**\n   * Create new patient\n   */ async createPatient(patientData) {\n        try {\n            this.validateTenantAccess();\n            const medicalRecordNumber = await this.generateMedicalRecordNumber();\n            const newPatient = {\n                ...patientData,\n                medicalRecordNumber,\n                totalVisits: 0,\n                status: \"active\",\n                dentalChart: this.initializeDentalChart(),\n                clinicalImages: [],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)(this.getCollection(\"patients\"), newPatient);\n            return docRef.id;\n        } catch (error) {\n            this.handleError(error, \"create patient\");\n        }\n    }\n    /**\n   * Initialize dental chart with all 32 teeth\n   */ initializeDentalChart() {\n        const dentalChart = [];\n        for(let i = 1; i <= 32; i++){\n            dentalChart.push({\n                toothNumber: i,\n                condition: \"healthy\",\n                notes: \"\",\n                updatedAt: new Date().toISOString()\n            });\n        }\n        return dentalChart;\n    }\n    /**\n   * Get patient by ID\n   */ async getPatient(id) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"patients\", id);\n            const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n            if (docSnap.exists()) {\n                return {\n                    id: docSnap.id,\n                    ...docSnap.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            this.handleError(error, \"get patient\");\n        }\n    }\n    /**\n   * Get all patients for current tenant\n   */ async getPatients() {\n        try {\n            this.validateTenantAccess();\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"patients\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"name\"));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            return querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n        } catch (error) {\n            this.handleError(error, \"get patients\");\n        }\n    }\n    /**\n   * Search patients\n   */ async searchPatients(searchTerm) {\n        try {\n            this.validateTenantAccess();\n            // Get all patients first (client-side filtering for now)\n            const patients = await this.getPatients();\n            const lowercaseSearch = searchTerm.toLowerCase();\n            return patients.filter((patient)=>patient.name.toLowerCase().includes(lowercaseSearch) || patient.medicalRecordNumber.toLowerCase().includes(lowercaseSearch) || patient.phone.includes(searchTerm) || patient.email.toLowerCase().includes(lowercaseSearch));\n        } catch (error) {\n            this.handleError(error, \"search patients\");\n        }\n    }\n    /**\n   * Update patient\n   */ async updatePatient(id, updates) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"patients\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(docRef, {\n                ...updates,\n                updatedAt: new Date().toISOString()\n            });\n        } catch (error) {\n            this.handleError(error, \"update patient\");\n        }\n    }\n    /**\n   * Delete patient\n   */ async deletePatient(id) {\n        try {\n            this.validateTenantAccess();\n            const docRef = this.getDocument(\"patients\", id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)(docRef);\n        } catch (error) {\n            this.handleError(error, \"delete patient\");\n        }\n    }\n    /**\n   * Add clinical image to patient\n   */ async addClinicalImage(patientId, imageData) {\n        try {\n            this.validateTenantAccess();\n            const patient = await this.getPatient(patientId);\n            if (!patient) throw new Error(\"Patient not found\");\n            const newImage = {\n                id: Date.now().toString(),\n                ...imageData,\n                date: new Date().toISOString()\n            };\n            const updatedImages = [\n                ...patient.clinicalImages || [],\n                newImage\n            ];\n            await this.updatePatient(patientId, {\n                clinicalImages: updatedImages\n            });\n        } catch (error) {\n            this.handleError(error, \"add clinical image\");\n        }\n    }\n    /**\n   * Update dental chart\n   */ async updateDentalChart(patientId, toothNumber, condition, notes) {\n        try {\n            this.validateTenantAccess();\n            const patient = await this.getPatient(patientId);\n            if (!patient) throw new Error(\"Patient not found\");\n            const updatedChart = patient.dentalChart?.map((tooth)=>tooth.toothNumber === toothNumber ? {\n                    ...tooth,\n                    condition,\n                    notes: notes || tooth.notes,\n                    updatedAt: new Date().toISOString()\n                } : tooth) || [];\n            await this.updatePatient(patientId, {\n                dentalChart: updatedChart\n            });\n        } catch (error) {\n            this.handleError(error, \"update dental chart\");\n        }\n    }\n    /**\n   * Real-time subscription to patients\n   */ subscribeToPatients(callback) {\n        this.validateTenantAccess();\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(this.getCollection(\"patients\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)(\"name\"));\n        return (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.onSnapshot)(q, (snapshot)=>{\n            const patients = snapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            callback(patients);\n        }, (error)=>{\n            console.error(\"Error in patients subscription:\", error);\n        });\n    }\n} // Note: PatientService now requires tenantId, so we'll create instances via TenantServiceRegistry\n // export const patientService = new PatientService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/patients.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1a89328312eb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YWM2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFhODkzMjgzMTJlYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\nconst metadata = {\n    title: \"DentalCare - Sistem Manajemen Klinik Gigi\",\n    description: \"Aplikasi manajemen klinik gigi yang terintegrasi\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\dentalcare.id\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1QjtBQUVpQjtBQUVqQyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsaURBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVudGFsLWNsaW5pYy11aS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tICcuL3Byb3ZpZGVycyc7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRGVudGFsQ2FyZSAtIFNpc3RlbSBNYW5hamVtZW4gS2xpbmlrIEdpZ2knLFxuICBkZXNjcmlwdGlvbjogJ0FwbGlrYXNpIG1hbmFqZW1lbiBrbGluaWsgZ2lnaSB5YW5nIHRlcmludGVncmFzaScsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImlkXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\dentalcare.id\src\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/@tanstack","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/@heroicons","vendor-chunks/lodash.camelcase","vendor-chunks/idb","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cdentalcare.id%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cdentalcare.id&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();