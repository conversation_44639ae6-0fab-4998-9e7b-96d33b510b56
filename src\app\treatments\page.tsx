'use client';

import { useState } from 'react';
import Header from '@/components/Layout/Header';
import { useTreatments } from '@/hooks/useTreatments';
import { Treatment } from '@/types';
import { 
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export default function TreatmentsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Use real data from Firebase
  const { data: treatments = [], isLoading, error } = useTreatments();

  const categories = ['all', ...Array.from(new Set(treatments.map(t => t.category)))];

  const filteredTreatments = treatments.filter(treatment => {
    const matchesSearch = treatment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         treatment.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || treatment.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Konsultasi': 'bg-blue-100 text-blue-800',
      'Preventif': 'bg-green-100 text-green-800',
      'Restoratif': 'bg-yellow-100 text-yellow-800',
      'Prostetik': 'bg-purple-100 text-purple-800',
      'Bedah': 'bg-red-100 text-red-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="flex-1 overflow-auto">
      <Header 
        title="Manajemen Treatment" 
        subtitle="Kelola katalog treatment dan tarif"
      />
      
      <main className="p-6">
        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-2 text-gray-600">Memuat data treatment...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <span className="text-red-800 font-medium">
                Error memuat data: {error.message}
              </span>
            </div>
          </div>
        )}

        {!isLoading && !error && (
          <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Katalog Treatment</h2>
            <button className="btn-primary flex items-center">
              <PlusIcon className="w-4 h-4 mr-2" />
              Tambah Treatment
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="relative flex-1">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Cari treatment berdasarkan nama atau kode..."
                className="input-field pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            {/* Category Filter */}
            <select
              className="input-field w-full sm:w-48"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="all">Semua Kategori</option>
              {categories.filter(cat => cat !== 'all').map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Treatment Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTreatments.map((treatment) => (
              <div key={treatment.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`status-badge ${getCategoryColor(treatment.category)}`}>
                        {treatment.category}
                      </span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{treatment.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">Kode: {treatment.code}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-gray-400 hover:text-gray-600">
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button className="text-gray-400 hover:text-red-600">
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <p className="text-gray-600 text-sm mb-4">{treatment.description}</p>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Tarif:</span>
                    <span className="text-lg font-bold text-primary-600">
                      {formatCurrency(treatment.price)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Durasi:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {treatment.duration} menit
                    </span>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button className="w-full btn-secondary text-sm">
                    Gunakan untuk Appointment
                  </button>
                </div>
              </div>
            ))}
          </div>

          {filteredTreatments.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada treatment ditemukan</h3>
              <p className="text-gray-600 mb-4">Coba ubah filter pencarian atau tambah treatment baru</p>
              <button className="btn-primary">
                Tambah Treatment Baru
              </button>
            </div>
          )}

          {/* Summary Stats */}
          {filteredTreatments.length > 0 && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{filteredTreatments.length}</p>
                  <p className="text-sm text-gray-600">Total Treatment</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{categories.length - 1}</p>
                  <p className="text-sm text-gray-600">Kategori</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(Math.min(...filteredTreatments.map(t => t.price)))}
                  </p>
                  <p className="text-sm text-gray-600">Tarif Terendah</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(Math.max(...filteredTreatments.map(t => t.price)))}
                  </p>
                  <p className="text-sm text-gray-600">Tarif Tertinggi</p>
                </div>
              </div>
            </div>
          )}
          </div>
        )}
      </main>
    </div>
  );
}
